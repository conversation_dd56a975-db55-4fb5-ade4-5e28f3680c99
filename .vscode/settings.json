{"tailwindCSS.experimental.classRegex": [["([\"'`][^\"'`]*.*?[\"'`])", "[\"'`]([^\"'`]*).*?[\"'`]"]], "eslint.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.codeAction.showDocumentation": {"enable": true}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "typescript.preferences.includePackageJsonAutoImports": "off", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}