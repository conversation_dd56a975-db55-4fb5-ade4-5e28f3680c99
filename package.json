{"name": "speakmcp", "version": "0.0.3", "type": "module", "description": "AI-powered dictation tool with MCP integration", "main": "./out/main/index.js", "author": "SpeakMCP", "homepage": "https://github.com/aj47/SpeakMCP", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "prepare": "husky", "start": "electron-vite preview", "dev": "electron-vite dev --watch", "build": "npm run typecheck && electron-vite build && npm run build-rs", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win --config electron-builder.config.cjs", "build:mac": "electron-vite build && electron-builder --mac --config electron-builder.config.cjs", "build:mac:signed": "npm run build && electron-builder --mac --config electron-builder.config.cjs --publish=never", "build:mac:universal": "npm run build && electron-builder --mac --universal --config electron-builder.config.cjs", "build:mas": "npm run build && electron-builder --mac mas --config electron-builder.config.cjs", "build:mas:dev": "npm run build && electron-builder --mac mas-dev --config electron-builder.config.cjs", "build:linux": "electron-vite build && electron-builder --linux --config electron-builder.config.cjs", "release": "node ./scripts/release.js", "setup:apple": "./scripts/setup-apple-signing.sh", "verify:signing": "./scripts/verify-signing.sh", "check:mas": "./scripts/check-mas-setup.sh", "upload:mas": "./scripts/build-and-upload-mas.sh", "fix-pnpm-windows": "node ./scripts/fix-pnpm-windows.js", "build-rs": "sh scripts/build-rs.sh"}, "dependencies": {"@egoist/electron-panel-window": "^8.0.3", "@modelcontextprotocol/sdk": "^1.13.3", "@radix-ui/react-scroll-area": "^1.2.9", "openai": "^5.10.2", "zod": "^3.25.76"}, "devDependencies": {"@egoist/tailwindcss-icons": "^1.8.1", "@egoist/tipc": "^0.3.2", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@electron-toolkit/utils": "^3.0.0", "@eslint/js": "^9.31.0", "@google/generative-ai": "^0.21.0", "@iconify-json/mingcute": "^1.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.59.14", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.14.8", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.20", "bumpp": "^9.7.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "electron": "^31.0.2", "electron-builder": "^24.13.3", "electron-updater": "^6.1.7", "electron-vite": "^2.3.0", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^16.1.2", "lucide-react": "^0.452.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.27.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.3", "tailwind-variants": "^0.2.1", "tailwindcss": "^3.4.13", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3", "vite": "^5.4.8", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.1.8"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "packageManager": "pnpm@9.12.1+sha512.e5a7e52a4183a02d5931057f7a0dbff9d5e9ce3161e33fa68ae392125b79282a8a8a470a51dfc8a0ed86221442eb2fb57019b0990ed24fab519bf0e1bc5ccfc4"}